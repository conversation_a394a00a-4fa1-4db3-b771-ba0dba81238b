using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.RestockReporting.Models;
using uBuyFirst.RestockReporting.Services;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Tests.RestockReporting.Services
{
    [TestClass]
    public class ItemHistoryExporterTests
    {
        private ItemHistoryExporter _exporter;
        private string _testBasePath;

        [TestInitialize]
        public void Setup()
        {
            // Create temporary test directories
            _testBasePath = Path.Combine(Path.GetTempPath(), "ItemHistoryExporterTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testBasePath);

            var options = new ItemHistoryOptions
            {
                BasePath = _testBasePath,
                CreateDailyFolders = true
            };

            _exporter = new ItemHistoryExporter(options);
        }

        [TestCleanup]
        public void Cleanup()
        {
            // Clean up test directories
            if (Directory.Exists(_testBasePath))
            {
                Directory.Delete(_testBasePath, true);
            }
        }

        [TestMethod]
        public async Task LoadHistoryAsync_WithNoFiles_ReturnsEmptyCollection()
        {
            // Arrange
            var startDate = new DateTime(2024, 12, 19);
            var endDate = new DateTime(2024, 12, 20);

            // Act
            var result = await _exporter.LoadHistoryAsync(startDate, endDate);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.Count());
        }

        [TestMethod]
        public async Task LoadHistoryAsync_WithValidFiles_ReturnsCorrectData()
        {
            // Arrange
            var testDate = new DateTime(2024, 12, 19);
            await CreateTestJsonFiles(testDate);

            // Act
            var result = await _exporter.LoadHistoryAsync(testDate, testDate);

            // Assert
            Assert.IsNotNull(result);
            var items = result.ToList();
            Assert.AreEqual(2, items.Count);
            Assert.IsTrue(items.Any(i => i.ItemData.ItemId == "123456789"));
            Assert.IsTrue(items.Any(i => i.ItemData.ItemId == "987654321"));
        }

        [TestMethod]
        public async Task ExportToCsvAsync_WithValidData_CreatesCorrectCsvFile()
        {
            // Arrange
            var testDate = new DateTime(2024, 12, 19);
            await CreateTestJsonFiles(testDate);
            var outputPath = Path.Combine(_testBasePath, "export.csv");

            // Act
            var resultPath = await _exporter.ExportToCsvAsync(testDate, testDate, outputPath);

            // Assert
            Assert.AreEqual(outputPath, resultPath);
            Assert.IsTrue(File.Exists(outputPath));

            var csvContent = File.ReadAllText(outputPath);
            Assert.IsTrue(csvContent.Contains("Timestamp,Outcome,Reason"), "Should contain CSV headers");
            Assert.IsTrue(csvContent.Contains("123456789"), "Should contain item data");
            Assert.IsTrue(csvContent.Contains("purchased"), "Should contain outcome data");
        }

        [TestMethod]
        public async Task ExportToCsvAsync_WithDateRange_FiltersCorrectly()
        {
            // Arrange
            var date1 = new DateTime(2024, 12, 19);
            var date2 = new DateTime(2024, 12, 20);
            var date3 = new DateTime(2024, 12, 21);

            await CreateTestJsonFiles(date1);
            await CreateTestJsonFiles(date2);
            await CreateTestJsonFiles(date3);

            var outputPath = Path.Combine(_testBasePath, "filtered_export.csv");

            // Act - Export only date1 and date2
            var resultPath = await _exporter.ExportToCsvAsync(date1, date2, outputPath);

            // Assert
            var csvContent = File.ReadAllText(outputPath);
            var lines = csvContent.Split('\n').Where(l => !string.IsNullOrWhiteSpace(l)).ToArray();

            // Should have header + 4 data rows (2 files per day * 2 days)
            Assert.AreEqual(5, lines.Length, "Should have header + 4 data rows");
        }

        [TestMethod]
        public async Task GetHistoryCountAsync_WithValidFiles_ReturnsCorrectCount()
        {
            // Arrange
            var testDate = new DateTime(2024, 12, 19);
            await CreateTestJsonFiles(testDate);

            // Act
            var count = await _exporter.GetHistoryCountAsync(testDate, testDate);

            // Assert
            Assert.AreEqual(2, count);
        }

        [TestMethod]
        public async Task ExportToCsvAsync_WithSpecialCharacters_EscapesCorrectly()
        {
            // Arrange
            var testDate = new DateTime(2024, 12, 19);
            var context = CreateTestContext("123456789", "Item with \"quotes\" and, commas");
            await SaveTestJsonFile(testDate, context);

            var outputPath = Path.Combine(_testBasePath, "special_chars.csv");

            // Act
            var resultPath = await _exporter.ExportToCsvAsync(testDate, testDate, outputPath);

            // Assert
            var csvContent = File.ReadAllText(outputPath);
            Assert.IsTrue(csvContent.Contains("\"Item with \"\"quotes\"\" and, commas\""),
                "Should properly escape quotes and wrap in quotes due to comma");
        }

        [TestMethod]
        public async Task LoadHistoryAsync_WithCorruptedFile_SkipsCorruptedAndContinues()
        {
            // Arrange
            var testDate = new DateTime(2024, 12, 19);
            var dayFolder = Path.Combine(_testBasePath, testDate.ToString("yyyy-MM-dd"));
            Directory.CreateDirectory(dayFolder);

            // Create one valid file
            var validContext = CreateTestContext("123456789", "Valid Item");
            var validFile = Path.Combine(dayFolder, "valid.json");
            File.WriteAllText(validFile, Newtonsoft.Json.JsonConvert.SerializeObject(validContext));

            // Create one corrupted file
            var corruptedFile = Path.Combine(dayFolder, "corrupted.json");
            File.WriteAllText(corruptedFile, "{ invalid json content");

            // Act
            var result = await _exporter.LoadHistoryAsync(testDate, testDate);

            // Assert
            var items = result.ToList();
            Assert.AreEqual(1, items.Count, "Should load only the valid file");
            Assert.AreEqual("123456789", items[0].ItemData.ItemId);
        }

        private async Task CreateTestJsonFiles(DateTime date)
        {
            var context1 = CreateTestContext("123456789", "Test Item 1");
            var context2 = CreateTestContext("987654321", "Test Item 2");

            await SaveTestJsonFile(date, context1);
            await SaveTestJsonFile(date, context2);
        }

        private async Task SaveTestJsonFile(DateTime date, ItemProcessingContext context)
        {
            var dayFolder = Path.Combine(_testBasePath, date.ToString("yyyy-MM-dd"));
            Directory.CreateDirectory(dayFolder);

            var fileName = $"Item_{context.ItemData.ItemId}_{context.KeywordState.KeywordId}_{date:HHmmss}.json";
            var filePath = Path.Combine(dayFolder, fileName);

            var json = Newtonsoft.Json.JsonConvert.SerializeObject(context, Newtonsoft.Json.Formatting.Indented);
            File.WriteAllText(filePath, json);
        }

        private ItemProcessingContext CreateTestContext(string itemId, string title)
        {
            return new ItemProcessingContext
            {
                Timestamp = new DateTime(2024, 12, 19, 14, 30, 22, DateTimeKind.Utc),
                Outcome = "purchased",
                Reason = "Test purchase",
                ItemData = new ItemHistoryData
                {
                    ItemId = itemId,
                    Title = title,
                    CurrentPrice = 25.99m,
                    Condition = "New",
                    Seller = "test-seller"
                },
                KeywordState = new KeywordSnapshot
                {
                    KeywordId = "kw-test-123",
                    Alias = "Test Keyword",
                    JobId = "JOB-001",
                    RequiredQuantity = 5,
                    PurchasedQuantity = 2
                },
                FilterRule = new FilterRuleContext
                {
                    FilterAlias = "Test Filter",
                    Expression = "Price <= 40",
                    Matched = true,
                    EvaluationResult = "Filter matched"
                },
                TransactionResult = new TransactionResult
                {
                    Attempted = true,
                    Success = true,
                    TransactionId = "txn-123",
                    PurchasePrice = 25.99m,
                    Quantity = 2
                }
            };
        }
    }
}
