using System;
using Newtonsoft.Json;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.RestockReporting.Models
{
    /// <summary>
    /// Complete context for an item processed by restock filters
    /// Contains all information needed for historical analysis
    /// </summary>
    public class ItemProcessingContext
    {
        /// <summary>
        /// When this item was processed (UTC)
        /// </summary>
        [JsonProperty("timestamp")]
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Outcome of processing: "purchased", "filtered_out", "error", "no_action"
        /// </summary>
        [JsonProperty("outcome")]
        public string Outcome { get; set; }

        /// <summary>
        /// Free text explanation of what happened
        /// </summary>
        [JsonProperty("reason")]
        public string Reason { get; set; }

        /// <summary>
        /// Complete eBay item data at time of processing
        /// </summary>
        [JsonProperty("itemData")]
        public ItemHistoryData ItemData { get; set; }

        /// <summary>
        /// Complete keyword state at time of processing
        /// </summary>
        [JsonProperty("keywordState")]
        public KeywordSnapshot KeywordState { get; set; }

        /// <summary>
        /// Filter rule evaluation context
        /// </summary>
        [JsonProperty("filterRule")]
        public FilterRuleContext FilterRule { get; set; }

        /// <summary>
        /// Transaction attempt and result information
        /// </summary>
        [JsonProperty("transactionResult")]
        public TransactionResult TransactionResult { get; set; }
    }

    /// <summary>
    /// Complete eBay item data captured at processing time
    /// </summary>
    public class ItemHistoryData
    {
        [JsonProperty("itemId")]
        public string ItemId { get; set; }

        [JsonProperty("title")]
        public string Title { get; set; }

        [JsonProperty("currentPrice")]
        public decimal CurrentPrice { get; set; }

        [JsonProperty("condition")]
        public string Condition { get; set; }

        [JsonProperty("seller")]
        public string Seller { get; set; }

        [JsonProperty("shippingCost")]
        public decimal ShippingCost { get; set; }

        [JsonProperty("location")]
        public string Location { get; set; }

        [JsonProperty("endTime")]
        public DateTime? EndTime { get; set; }

        [JsonProperty("buyItNowPrice")]
        public decimal? BuyItNowPrice { get; set; }

        [JsonProperty("auctionPrice")]
        public decimal? AuctionPrice { get; set; }

        [JsonProperty("quantityAvailable")]
        public int QuantityAvailable { get; set; }

        [JsonProperty("listingType")]
        public string ListingType { get; set; }

        [JsonProperty("itemUrl")]
        public string ItemUrl { get; set; }

        [JsonProperty("imageUrl")]
        public string ImageUrl { get; set; }

        // Additional properties can be added as needed
        [JsonProperty("categoryId")]
        public string CategoryId { get; set; }

        [JsonProperty("categoryName")]
        public string CategoryName { get; set; }

        [JsonProperty("conditionDescription")]
        public string ConditionDescription { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("feedbackScore")]
        public decimal? FeedbackScore { get; set; }

        [JsonProperty("feedbackRating")]
        public decimal? FeedbackRating { get; set; }

        [JsonProperty("bids")]
        public int? Bids { get; set; }

        [JsonProperty("bestOffer")]
        public bool? BestOffer { get; set; }

        [JsonProperty("autoPay")]
        public bool? AutoPay { get; set; }

        [JsonProperty("site")]
        public string Site { get; set; }
    }

    /// <summary>
    /// Filter rule evaluation context
    /// </summary>
    public class FilterRuleContext
    {
        [JsonProperty("filterAlias")]
        public string FilterAlias { get; set; }

        [JsonProperty("expression")]
        public string Expression { get; set; }

        [JsonProperty("matched")]
        public bool Matched { get; set; }

        [JsonProperty("evaluationResult")]
        public string EvaluationResult { get; set; }

        [JsonProperty("evaluatedAt")]
        public DateTime EvaluatedAt { get; set; }
    }

    /// <summary>
    /// Transaction attempt and result information
    /// </summary>
    public class TransactionResult
    {
        [JsonProperty("attempted")]
        public bool Attempted { get; set; }

        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("transactionId")]
        public string TransactionId { get; set; }

        [JsonProperty("errorMessage")]
        public string ErrorMessage { get; set; }

        [JsonProperty("purchasePrice")]
        public decimal? PurchasePrice { get; set; }

        [JsonProperty("quantity")]
        public int? Quantity { get; set; }

        [JsonProperty("completedAt")]
        public DateTime? CompletedAt { get; set; }
    }
}
