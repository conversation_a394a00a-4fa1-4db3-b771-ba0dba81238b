using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using uBuyFirst.RestockReporting.Models;

namespace uBuyFirst.RestockReporting.Services
{
    /// <summary>
    /// Service for exporting item processing history to CSV format
    /// </summary>
    public class ItemHistoryExporter : IItemHistoryExporter
    {
        private readonly ItemHistoryOptions _options;

        public ItemHistoryExporter(ItemHistoryOptions options)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));
        }

        /// <summary>
        /// Exports item processing history to CSV format
        /// </summary>
        public async Task<string> ExportToCsvAsync(DateTime startDate, DateTime endDate, string outputPath)
        {
            var items = await LoadHistoryAsync(startDate, endDate);
            var csv = GenerateCsv(items);

            await Task.Run(() => File.WriteAllText(outputPath, csv));
            return outputPath;
        }

        /// <summary>
        /// Loads item processing history from JSON files for a date range
        /// </summary>
        public async Task<IEnumerable<ItemProcessingContext>> LoadHistoryAsync(DateTime startDate, DateTime endDate)
        {
            var items = new List<ItemProcessingContext>();

            for (var date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
            {
                var dayFolder = Path.Combine(_options.BasePath, date.ToString("yyyy-MM-dd"));
                if (!Directory.Exists(dayFolder)) continue;

                var jsonFiles = Directory.GetFiles(dayFolder, "*.json");

                foreach (var file in jsonFiles)
                {
                    try
                    {
                        var json = await Task.Run(() => File.ReadAllText(file));
                        var item = JsonConvert.DeserializeObject<ItemProcessingContext>(json);
                        if (item != null && item.Timestamp >= startDate && item.Timestamp <= endDate)
                        {
                            items.Add(item);
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log corrupted file but continue processing
                        Console.WriteLine($"Failed to read {file}: {ex.Message}");
                    }
                }
            }

            return items.OrderBy(x => x.Timestamp);
        }

        /// <summary>
        /// Gets the count of available history records for a date range
        /// </summary>
        public async Task<int> GetHistoryCountAsync(DateTime startDate, DateTime endDate)
        {
            var items = await LoadHistoryAsync(startDate, endDate);
            return items.Count();
        }

        /// <summary>
        /// Generates CSV content from item processing contexts
        /// </summary>
        private string GenerateCsv(IEnumerable<ItemProcessingContext> items)
        {
            var csv = new StringBuilder();

            // CSV Headers - All available properties flattened
            csv.AppendLine(string.Join(",", new[]
            {
                "Timestamp", "Outcome", "Reason",
                "ItemId", "Title", "CurrentPrice", "Condition", "Seller", "ShippingCost",
                "Location", "EndTime", "ItemPrice", "AuctionPrice", "QuantityAvailable",
                "ListingType", "ItemUrl", "ImageUrl", "CategoryId", "CategoryName",
                "ConditionDescription", "Description", "FeedbackScore", "FeedbackRating",
                "Bids", "BestOffer", "AutoPay", "Site",
                "StartTime", "FoundTimeSeconds", "FromCountry", "SellerCountry", "ShippingDays",
                "DispatchDays", "ShippingDelivery", "ShippingType", "TotalPrice", "Brand",
                "UPC", "Model", "MPN", "SellerIsBusiness",
                "KeywordId", "Alias", "JobId", "Keywords", "RequiredQuantity", "PurchasedQuantity",
                "PriceMin", "PriceMax", "Condition_Filter", "Sellers", "SellerType",
                "EbaySiteName", "LocatedIn", "AvailableTo", "Zip", "SearchInDescription",
                "Categories", "ViewName", "ListingType_Filter",
                "FilterAlias", "Expression", "Matched", "EvaluationResult", "EvaluatedAt",
                "TransactionAttempted", "TransactionSuccess", "TransactionError",
                "PurchasePrice", "PurchaseQuantity", "CompletedAt"
            }));

            // CSV Data Rows
            foreach (var item in items)
            {
                csv.AppendLine(string.Join(",", new[]
                {
                    EscapeCsv(item.Timestamp.ToString("yyyy-MM-dd HH:mm:ss")),
                    EscapeCsv(item.Outcome),
                    EscapeCsv(item.Reason),
                    EscapeCsv(item.ItemData?.ItemId),
                    EscapeCsv(item.ItemData?.Title),
                    item.ItemData?.CurrentPrice.ToString() ?? "",
                    EscapeCsv(item.ItemData?.Condition),
                    EscapeCsv(item.ItemData?.Seller),
                    item.ItemData?.ShippingCost.ToString() ?? "",
                    EscapeCsv(item.ItemData?.Location),
                    item.ItemData?.EndTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "",
                    item.ItemData?.ItemPrice?.ToString() ?? "",
                    item.ItemData?.AuctionPrice?.ToString() ?? "",
                    item.ItemData?.QuantityAvailable.ToString() ?? "",
                    EscapeCsv(item.ItemData?.ListingType),
                    EscapeCsv(item.ItemData?.ItemUrl),
                    EscapeCsv(item.ItemData?.ImageUrl),
                    EscapeCsv(item.ItemData?.CategoryId),
                    EscapeCsv(item.ItemData?.CategoryName),
                    EscapeCsv(item.ItemData?.ConditionDescription),
                    EscapeCsv(item.ItemData?.Description),
                    item.ItemData?.FeedbackScore?.ToString() ?? "",
                    item.ItemData?.FeedbackRating?.ToString() ?? "",
                    item.ItemData?.Bids?.ToString() ?? "",
                    item.ItemData?.BestOffer?.ToString() ?? "",
                    item.ItemData?.AutoPay?.ToString() ?? "",
                    EscapeCsv(item.ItemData?.Site),
                    item.ItemData?.StartTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "",
                    item.ItemData?.FoundTimeSeconds?.ToString() ?? "",
                    EscapeCsv(item.ItemData?.FromCountry),
                    EscapeCsv(item.ItemData?.SellerCountry),
                    item.ItemData?.ShippingDays?.ToString() ?? "",
                    item.ItemData?.DispatchDays?.ToString() ?? "",
                    EscapeCsv(item.ItemData?.ShippingDelivery),
                    EscapeCsv(item.ItemData?.ShippingType),
                    item.ItemData?.TotalPrice?.ToString() ?? "",
                    EscapeCsv(item.ItemData?.Brand),
                    EscapeCsv(item.ItemData?.UPC),
                    EscapeCsv(item.ItemData?.Model),
                    EscapeCsv(item.ItemData?.MPN),
                    item.ItemData?.SellerIsBusiness?.ToString() ?? "",
                    EscapeCsv(item.KeywordState?.KeywordId),
                    EscapeCsv(item.KeywordState?.Alias),
                    EscapeCsv(item.KeywordState?.JobId),
                    EscapeCsv(item.KeywordState?.Keywords),
                    item.KeywordState?.RequiredQuantity.ToString() ?? "",
                    item.KeywordState?.PurchasedQuantity.ToString() ?? "",
                    item.KeywordState?.PriceMin.ToString() ?? "",
                    item.KeywordState?.PriceMax.ToString() ?? "",
                    EscapeCsv(string.Join(";", item.KeywordState?.Condition ?? new string[0])),
                    EscapeCsv(string.Join(",", item.KeywordState?.Sellers ?? new string[0])),
                    EscapeCsv(item.KeywordState?.SellerType),
                    EscapeCsv(item.KeywordState?.EbaySiteName),
                    EscapeCsv(item.KeywordState?.LocatedIn),
                    EscapeCsv(item.KeywordState?.AvailableTo),
                    EscapeCsv(item.KeywordState?.Zip),
                    item.KeywordState?.SearchInDescription.ToString() ?? "",
                    EscapeCsv(item.KeywordState?.Categories),
                    EscapeCsv(item.KeywordState?.ViewName),
                    EscapeCsv(string.Join(",", item.KeywordState?.ListingType ?? new string[0])),
                    EscapeCsv(item.FilterRule?.FilterAlias),
                    EscapeCsv(item.FilterRule?.Expression),
                    item.FilterRule?.Matched.ToString() ?? "",
                    EscapeCsv(item.FilterRule?.EvaluationResult),
                    item.FilterRule?.EvaluatedAt.ToString("yyyy-MM-dd HH:mm:ss") ?? "",
                    item.TransactionResult?.Attempted.ToString() ?? "",
                    item.TransactionResult?.Success.ToString() ?? "",
                    EscapeCsv(item.TransactionResult?.ErrorMessage),
                    item.TransactionResult?.PurchasePrice?.ToString() ?? "",
                    item.TransactionResult?.Quantity?.ToString() ?? "",
                    item.TransactionResult?.CompletedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? ""
                }));
            }

            return csv.ToString();
        }

        /// <summary>
        /// Escapes CSV values by wrapping in quotes and escaping internal quotes
        /// </summary>
        private string EscapeCsv(string value)
        {
            if (string.IsNullOrEmpty(value)) return "";

            // Escape quotes and wrap in quotes if contains comma, quote, or newline
            if (value.Contains(",") || value.Contains("\"") || value.Contains("\n") || value.Contains("\r"))
            {
                return "\"" + value.Replace("\"", "\"\"") + "\"";
            }

            return value;
        }
    }
}
